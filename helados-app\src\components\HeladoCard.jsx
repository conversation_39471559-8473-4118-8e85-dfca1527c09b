import React from 'react'
import './HeladoCard.css'

function HeladoCard({ helado, onClick }) {
  return (
    <div className="helado-card" onClick={onClick}>
      <div className="card-image-container">
        <img 
          src={helado.imagen} 
          alt={helado.nombre}
          className="card-image"
        />
        <div className="card-overlay">
          <span className="material-icons">visibility</span>
          <span>Ver Receta</span>
        </div>
      </div>
      
      <div className="card-content">
        <h3 className="card-title">{helado.nombre}</h3>
        <p className="card-sabor">{helado.sabor}</p>
        
        <div className="card-meta">
          <div className="meta-item">
            <span className="material-icons">schedule</span>
            <span>{helado.tiempo}</span>
          </div>
          <div className="meta-item">
            <span className="material-icons">restaurant</span>
            <span>{helado.porciones}</span>
          </div>
        </div>
        
        <div className="card-tags">
          {helado.tags.map((tag, index) => (
            <span key={index} className="tag">{tag}</span>
          ))}
        </div>
      </div>
    </div>
  )
}

export default HeladoCard
