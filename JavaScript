// script.js

// 1. **Colección de Recetas (Nuestra "Base de Datos" en JS)**
//    Añadimos más helados para que la galería tenga variedad.
const recetasHelados = {
    'heladoChocolate': {
        id: 'heladoChocolate', // Usamos el ID como clave para fácil acceso
        nombre: 'Helado Casero de Chocolate (Sin Máquina)',
        urlImagen: 'https://cdn.pixabay.com/photo/2016/09/20/19/22/ice-cream-1683935_1280.jpg',
        tiempoPreparacion: '15 min + 6 horas de congelación',
        porciones: '4-6 porciones',
        ingredientes: [
            { nombre: 'Crema de leche (para montar)', cantidad: '500', unidad: 'ml' },
            { nombre: 'Leche condensada', cantidad: '300', unidad: 'ml' },
            { nombre: 'Cacao en polvo sin azúcar', cantidad: '4', unidad: 'cucharadas' },
            { nombre: 'Extracto de vainilla', cantidad: '1', unidad: 'cucharadita' },
            { nombre: 'Chispas de chocolate (opcional)', cantidad: '50', unidad: 'gramos' },
        ],
        instrucciones: [
            'En un bol grande y frío, vierte la crema de leche. Bate con una batidora eléctrica a velocidad media-alta hasta que la crema esté firme y forme picos suaves.',
            'En otro bol, mezcla la leche condensada, el cacao en polvo y el extracto de vainilla. Bate hasta que no queden grumos y la mezcla esté homogénea y brillante.',
            'Con una espátula, incorpora suavemente la crema batida a la mezcla de chocolate. Hazlo en movimientos envolventes para no bajar el volumen de la crema. Si usas chispas de chocolate, agrégalas ahora.',
            'Vierte la mezcla en un recipiente apto para congelador, preferiblemente uno de metal para una congelación más rápida. Cubre la superficie con papel film tocando el helado para evitar la formación de cristales de hielo.',
            'Congela por un mínimo de 6 horas, o idealmente toda la noche, hasta que esté firme.',
            'Antes de servir, deja el helado a temperatura ambiente por unos 5-10 minutos para que sea más fácil de sacar y disfrutar.'
        ],
        esFavorita: false
    },
    'heladoFresa': {
        id: 'heladoFresa',
        nombre: 'Helado Casero de Fresa (Sin Máquina)',
        urlImagen: 'https://cdn.pixabay.com/photo/2016/06/17/17/20/strawberry-ice-cream-1463110_1280.jpg',
        tiempoPreparacion: '15 min + 6 horas de congelación',
        porciones: '4-6 porciones',
        ingredientes: [
            { nombre: 'Crema de leche (para montar)', cantidad: '500', unidad: 'ml' },
            { nombre: 'Leche condensada', cantidad: '300', unidad: 'ml' },
            { nombre: 'Fresas frescas (trituradas)', cantidad: '200', unidad: 'gramos' },
            { nombre: 'Extracto de vainilla', cantidad: '1', unidad: 'cucharadita' },
        ],
        instrucciones: [
            'Lava y tritura las fresas hasta obtener un puré suave. Puedes colar si prefieres sin semillas.',
            'En un bol grande y frío, bate la crema de leche hasta que esté firme.',
            'En otro bol, mezcla la leche condensada, el puré de fresa y el extracto de vainilla hasta que se integren bien.',
            'Con una espátula, incorpora suavemente la crema batida a la mezcla de fresa con movimientos envolventes.',
            'Vierte la mezcla en un recipiente apto para congelador. Cubre con papel film y congela por un mínimo de 6 horas.',
            'Sirve y disfruta de tu helado de fresa casero.'
        ],
        esFavorita: false
    },
    'heladoVainilla': {
        id: 'heladoVainilla',
        nombre: 'Helado Casero de Vainilla (Sin Máquina)',
        urlImagen: 'https://cdn.pixabay.com/photo/2017/04/20/19/29/ice-cream-2246706_1280.jpg',
        tiempoPreparacion: '10 min + 6 horas de congelación',
        porciones: '4-6 porciones',
        ingredientes: [
            { nombre: 'Crema de leche (para montar)', cantidad: '500', unidad: 'ml' },
            { nombre: 'Leche condensada', cantidad: '300', unidad: 'ml' },
            { nombre: 'Extracto puro de vainilla', cantidad: '2', unidad: 'cucharaditas' },
        ],
        instrucciones: [
            'En un bol grande y frío, bate la crema de leche hasta que esté firme y forme picos suaves.',
            'En otro bol, mezcla la leche condensada con el extracto de vainilla hasta que se integren bien.',
            'Con una espátula, incorpora suavemente la crema batida a la mezcla de vainilla con movimientos envolventes.',
            'Vierte la mezcla en un recipiente apto para congelador. Cubre con papel film y congela por un mínimo de 6 horas.',
            'Sirve y disfruta de tu clásico helado de vainilla casero.'
        ],
        esFavorita: false
    },
    'heladoBanana': {
        id: 'heladoBanana',
        nombre: 'Helado Casero de Banana Saludable (Sin Máquina)',
        urlImagen: 'https://cdn.pixabay.com/photo/2016/03/23/15/43/banana-1275218_1280.jpg',
        tiempoPreparacion: '5 min + 2-3 horas de congelación (de la fruta)',
        porciones: '2 porciones',
        ingredientes: [
            { nombre: 'Bananas maduras (congeladas)', cantidad: '3', unidad: 'unidades' },
            { nombre: 'Cacao en polvo (opcional)', cantidad: '1', unidad: 'cucharada' },
            { nombre: 'Miel o sirope de arce (opcional)', cantidad: '1', unidad: 'cucharada' },
        ],
        instrucciones: [
            'Pela y corta las bananas en rodajas. Colócalas en un recipiente y congélalas por al menos 2-3 horas, o hasta que estén firmes.',
            'Coloca las rodajas de banana congeladas en una licuadora o procesador de alimentos.',
            'Procesa hasta que las bananas se conviertan en una crema suave y con textura de helado. Raspa los lados si es necesario.',
            'Si deseas, añade el cacao en polvo y/o la miel y procesa brevemente hasta que se incorporen.',
            'Sirve inmediatamente para una textura suave de "soft-serve", o congela por 30 minutos más para una consistencia más firme.'
        ],
        esFavorita: false
    },
    'heladoMangoMaracuya': {
        id: 'heladoMangoMaracuya',
        nombre: 'Helado Cremoso de Mango y Maracuyá',
        urlImagen: 'https://cdn.pixabay.com/photo/2017/08/29/19/29/ice-cream-2694142_1280.jpg',
        tiempoPreparacion: '20 min + 8 horas de congelación',
        porciones: '4-6 porciones',
        ingredientes: [
            { nombre: 'Crema de leche (para montar)', cantidad: '500', unidad: 'ml' },
            { nombre: 'Leche condensada', cantidad: '300', unidad: 'ml' },
            { nombre: 'Pulpa de mango maduro', cantidad: '200', unidad: 'gramos' },
            { nombre: 'Pulpa de maracuyá (fruta de la pasión)', cantidad: '100', unidad: 'ml' },
        ],
        instrucciones: [
            'Si usas mango fresco, pélalo y tritúralo hasta obtener un puré suave. Cuela la pulpa de maracuyá para quitar las semillas.',
            'En un bol grande y frío, bate la crema de leche hasta que esté firme y forme picos suaves.',
            'En otro bol, mezcla la leche condensada con el puré de mango y la pulpa de maracuyá hasta que se integren bien.',
            'Con una espátula, incorpora suavemente la crema batida a la mezcla de frutas con movimientos envolventes.',
            'Vierte la mezcla en un recipiente apto para congelador. Cubre con papel film y congela por un mínimo de 8 horas o toda la noche.',
            'Deja a temperatura ambiente unos minutos antes de servir para una textura perfecta.'
        ],
        esFavorita: false
    },
    'heladoKiwiLima': {
        id: 'heladoKiwiLima',
        nombre: 'Sorbet de Kiwi y Lima Refrescante',
        urlImagen: 'https://cdn.pixabay.com/photo/2017/06/17/14/56/sorbet-2412497_1280.jpg',
        tiempoPreparacion: '15 min + 4 horas de congelación (con intervalos)',
        porciones: '3-4 porciones',
        ingredientes: [
            { nombre: 'Kiwis maduros', cantidad: '4', unidad: 'unidades' },
            { nombre: 'Jugo de lima fresco', cantidad: '2', unidad: 'cucharadas' },
            { nombre: 'Azúcar (o edulcorante al gusto)', cantidad: '1/2', unidad: 'taza' },
            { nombre: 'Agua', cantidad: '1/4', unidad: 'taza' },
        ],
        instrucciones: [
            'Pela y tritura los kiwis hasta obtener un puré. Cuela si prefieres un sorbet sin semillas.',
            'En una olla pequeña, mezcla el azúcar y el agua. Calienta a fuego medio, revolviendo hasta que el azúcar se disuelva completamente. Deja enfriar.',
            'En un bol, combina el puré de kiwi, el jugo de lima y el almíbar de azúcar frío. Mezcla bien.',
            'Vierte la mezcla en un recipiente poco profundo apto para congelador. Congela por 4 horas, revolviendo cada hora para romper los cristales de hielo.',
            'Sirve directamente del congelador. ¡Ideal para un día caluroso!'
        ],
        esFavorita: false
    }
    // Puedes seguir añadiendo más recetas aquí siguiendo el mismo formato
};

// ** Importante: Definir el elemento donde se cargan las recetas individualmente **
let recetaActual = null;

// ** Lógica para la Página Principal (`index.html`) **
if (document.getElementById('contenedorHelados')) {
    const contenedorHelados = document.getElementById('contenedorHelados');

    for (const id in recetasHelados) {
        const receta = recetasHelados[id];
        const cardHtml = `
            <div class="card-helado" data-id="${receta.id}">
                <img src="${receta.urlImagen}" alt="${receta.nombre}">
                <div class="card-helado-content">
                    <h3>${receta.nombre}</h3>
                    <p class="card-meta">Tiempo: ${receta.tiempoPreparacion}</p>
                    <a href="receta.html?id=${receta.id}" class="btn-ver-receta">Ver Receta</a>
                </div>
            </div>
        `;
        contenedorHelados.insertAdjacentHTML('beforeend', cardHtml);
    }

    // Añadir event listeners a las tarjetas si se quisiera otra interactividad
    // const cards = document.querySelectorAll('.card-helado');
    // cards.forEach(card => {
    //     card.addEventListener('click', () => {
    //         const id = card.dataset.id;
    //         // Opcional: Podríamos navegar directamente sin el botón, pero el botón es más claro.
    //         window.location.href = `receta.html?id=${id}`;
    //     });
    // });
}

// ** Lógica para la Página de Receta Individual (`receta.html`) **
// Esta parte del script solo se ejecutará si estamos en 'receta.html'
if (document.querySelector('.contenedor-receta')) {
    // 2. Referencias a Elementos HTML (solo para receta.html)
    const imagenReceta = document.querySelector('.imagen-receta');
    const nombreReceta = document.getElementById('nombreReceta');
    const tiempoPreparacionSpan = document.getElementById('tiempoPreparacion');
    const porcionesSpan = document.getElementById('porciones');
    const listaIngredientes = document.getElementById('listaIngredientes');
    const listaInstrucciones = document.getElementById('listaInstrucciones');
    const btnFavorito = document.getElementById('btnFavorito');
    const btnListaCompras = document.getElementById('btnListaCompras');

    // 3. Función para cargar los datos de CUALQUIER receta en la página de detalle
    function cargarDatosReceta(receta) {
        recetaActual = receta; // Establecemos la receta actual que se está visualizando

        imagenReceta.src = receta.urlImagen;
        imagenReceta.alt = receta.nombre;
        nombreReceta.textContent = receta.nombre;
        tiempoPreparacionSpan.textContent = receta.tiempoPreparacion;
        porcionesSpan.textContent = receta.porciones;

        listaIngredientes.innerHTML = '';
        receta.ingredientes.forEach(ingrediente => {
            const li = document.createElement('li');
            li.textContent = `${ingrediente.cantidad} ${ingrediente.unidad} de ${ingrediente.nombre}`;
            listaIngredientes.appendChild(li);
        });

        listaInstrucciones.innerHTML = '';
        receta.instrucciones.forEach((instruccion) => {
            const li = document.createElement('li');
            li.textContent = instruccion;
            listaInstrucciones.appendChild(li);
        });

        // Actualizar el estado visual del botón de favoritos
        if (receta.esFavorita) {
            btnFavorito.classList.add('activo');
        } else {
            btnFavorito.classList.remove('activo');
        }
    }

    // 4. Lógica para el botón de Favoritos
    btnFavorito.addEventListener('click', () => {
        if (!recetaActual) {
            console.error('No hay receta cargada para marcar como favorita.');
            return;
        }

        recetaActual.esFavorita = !recetaActual.esFavorita;

        // Actualiza solo el estilo del botón
        if (recetaActual.esFavorita) {
            btnFavorito.classList.add('activo');
            alert(`¡"${recetaActual.nombre}" añadido a tus favoritos!`);
        } else {
            btnFavorito.classList.remove('activo');
            alert(`"${recetaActual.nombre}" eliminado de tus favoritos.`);
        }
        console.log(`Estado de favoritos de "${recetaActual.nombre}": ${recetaActual.esFavorita}`);
        // En una app real, guardarías este estado en localStorage o un servidor
    });

    // 5. Lógica para el botón "Añadir a Lista de Compras"
    btnListaCompras.addEventListener('click', () => {
        if (!recetaActual) {
            console.error('No hay receta cargada para añadir a la lista de compras.');
            return;
        }
        alert(`Funcionalidad "Añadir a Lista de Compras" para "${recetaActual.nombre}" en desarrollo. ¡Pronto podrás planificar tus compras!`);
        console.log(`Ingredientes de "${recetaActual.nombre}" para añadir a la lista:`, recetaActual.ingredientes);
    });

    // 6. **Cargar la receta correcta al iniciar la página de receta.html**
    document.addEventListener('DOMContentLoaded', () => {
        // Obtenemos el ID de la URL (ej: receta.html?id=heladoFresa)
        const params = new URLSearchParams(window.location.search);
        const recetaId = params.get('id');

        if (recetaId && recetasHelados[recetaId]) {
            cargarDatosReceta(recetasHelados[recetaId]);
        } else {
            console.error('No se pudo cargar la receta. ID no encontrado o no especificado.');
            // Opcional: redirigir a la página principal o mostrar un mensaje de error
            nombreReceta.textContent = "Receta no encontrada";
            // Puedes ocultar el resto de la interfaz o mostrar un mensaje amistoso
        }
    });
}