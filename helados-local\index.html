<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Helados Caseros - Recetas Deliciosas</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <span class="material-icons logo-icon">icecream</span>
                    <span class="logo-text">Helados Caseros</span>
                </div>
                
                <div class="search-container">
                    <div class="search-box">
                        <span class="material-icons search-icon">search</span>
                        <input
                            type="text"
                            placeholder="Buscar sabores de helado..."
                            id="searchInput"
                            class="search-input"
                        />
                        <button id="clearSearch" class="clear-search" style="display: none;">
                            <span class="material-icons">clear</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="hero-section">
                <h1 class="hero-title">
                    <span class="material-icons hero-icon">icecream</span>
                    Helados Caseros Deliciosos
                </h1>
                <p class="hero-subtitle">
                    Descubre recetas increíbles para hacer helados caseros sin máquina. 
                    ¡Sabores únicos y naturales para toda la familia!
                </p>
            </div>

            <div class="helados-grid" id="heladosGrid">
                <!-- Las tarjetas se generarán dinámicamente con JavaScript -->
            </div>

            <div class="no-results" id="noResults" style="display: none;">
                <span class="material-icons">search_off</span>
                <h3>No se encontraron helados</h3>
                <p>Intenta con otro término de búsqueda</p>
            </div>
        </main>
    </div>

    <!-- Modal de Receta -->
    <div class="modal-backdrop" id="modalBackdrop" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">Título de la Receta</h2>
                <button class="close-button" id="closeModal">
                    <span class="material-icons">close</span>
                </button>
            </div>
            
            <div class="modal-body">
                <div class="recipe-image-section">
                    <img id="modalImage" src="" alt="" class="recipe-image">
                    <div class="recipe-info">
                        <div class="info-item">
                            <span class="material-icons">schedule</span>
                            <div>
                                <strong>Tiempo de preparación</strong>
                                <p id="modalTiempo">-</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <span class="material-icons">restaurant</span>
                            <div>
                                <strong>Porciones</strong>
                                <p id="modalPorciones">-</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <span class="material-icons">star</span>
                            <div>
                                <strong>Dificultad</strong>
                                <p id="modalDificultad">-</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="recipe-details">
                    <div class="ingredients-section">
                        <h3>
                            <span class="material-icons">shopping_cart</span>
                            Ingredientes
                        </h3>
                        <ul class="ingredients-list" id="modalIngredientes">
                            <!-- Se llenarán dinámicamente -->
                        </ul>
                    </div>
                    
                    <div class="instructions-section">
                        <h3>
                            <span class="material-icons">list_alt</span>
                            Instrucciones
                        </h3>
                        <ol class="instructions-list" id="modalInstrucciones">
                            <!-- Se llenarán dinámicamente -->
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
