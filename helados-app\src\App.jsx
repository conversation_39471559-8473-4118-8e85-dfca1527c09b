import React, { useState } from 'react'
import Header from './components/Header'
import HeladoCard from './components/HeladoCard'
import RecetaModal from './components/RecetaModal'
import { helados } from './data/helados'
import './App.css'

function App() {
  const [selectedHelado, setSelectedHelado] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')

  const filteredHelados = helados.filter(helado =>
    helado.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
    helado.sabor.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const openModal = (helado) => {
    setSelectedHelado(helado)
  }

  const closeModal = () => {
    setSelectedHelado(null)
  }

  return (
    <div className="app">
      <Header searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      
      <main className="main-content">
        <div className="hero-section">
          <h1 className="hero-title">
            <span className="material-icons hero-icon">icecream</span>
            Helados Caseros Deliciosos
          </h1>
          <p className="hero-subtitle">
            Descubre recetas increíbles para hacer helados caseros sin máquina. 
            ¡Sabores únicos y naturales para toda la familia!
          </p>
        </div>

        <div className="helados-grid">
          {filteredHelados.map(helado => (
            <HeladoCard
              key={helado.id}
              helado={helado}
              onClick={() => openModal(helado)}
            />
          ))}
        </div>

        {filteredHelados.length === 0 && (
          <div className="no-results">
            <span className="material-icons">search_off</span>
            <h3>No se encontraron helados</h3>
            <p>Intenta con otro término de búsqueda</p>
          </div>
        )}
      </main>

      {selectedHelado && (
        <RecetaModal
          helado={selectedHelado}
          onClose={closeModal}
        />
      )}
    </div>
  )
}

export default App
