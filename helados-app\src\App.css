/* Estilos principales de la aplicación */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.main-content {
  padding: var(--spacing-lg) 0;
}

/* Sección hero */
.hero-section {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero-icon {
  font-size: 1.2em;
  color: var(--primary-red);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.hero-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Grid de helados */
.helados-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  padding: 0 var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

/* Estado sin resultados */
.no-results {
  text-align: center;
  padding: var(--spacing-xxl);
  color: var(--text-secondary);
}

.no-results .material-icons {
  font-size: 4rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.no-results p {
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
    padding: 0 var(--spacing-md);
  }
  
  .helados-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .main-content {
    padding: var(--spacing-md) 0;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: var(--spacing-lg) var(--spacing-sm);
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
}
