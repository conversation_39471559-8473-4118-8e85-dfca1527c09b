/* Estilos de las tarjetas de helado */
.helado-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.helado-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
}

/* Contenedor de imagen */
.card-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.helado-card:hover .card-image {
  transform: scale(1.1);
}

/* Overlay de la imagen */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(66, 133, 244, 0.9) 0%,
    rgba(234, 67, 53, 0.9) 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-weight: 600;
  gap: var(--spacing-xs);
}

.helado-card:hover .card-overlay {
  opacity: 1;
}

.card-overlay .material-icons {
  font-size: 2rem;
}

/* Contenido de la tarjeta */
.card-content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

.card-sabor {
  color: var(--primary-blue);
  font-weight: 500;
  font-size: 0.9rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Meta información */
.card-meta {
  display: flex;
  gap: var(--spacing-md);
  margin: var(--spacing-sm) 0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.meta-item .material-icons {
  font-size: 1rem;
  color: var(--text-tertiary);
}

/* Tags */
.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: auto;
}

.tag {
  background: var(--bg-tertiary);
  color: var(--primary-blue);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Estados especiales */
.helado-card:active {
  transform: translateY(-4px);
}

/* Responsive */
@media (max-width: 768px) {
  .card-image-container {
    height: 180px;
  }
  
  .card-content {
    padding: var(--spacing-md);
  }
  
  .card-title {
    font-size: 1.1rem;
  }
  
  .card-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 480px) {
  .card-image-container {
    height: 160px;
  }
  
  .card-content {
    padding: var(--spacing-sm);
  }
  
  .card-title {
    font-size: 1rem;
  }
  
  .card-sabor {
    font-size: 0.8rem;
  }
  
  .meta-item {
    font-size: 0.8rem;
  }
  
  .tag {
    font-size: 0.7rem;
    padding: 2px var(--spacing-xs);
  }
}
