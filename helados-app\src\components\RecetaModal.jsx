import React, { useEffect } from 'react'
import './RecetaModal.css'

function RecetaModal({ helado, onClose }) {
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }
    
    document.addEventListener('keydown', handleEscape)
    document.body.style.overflow = 'hidden'
    
    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [onClose])

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-content">
        <div className="modal-header">
          <h2 className="modal-title">{helado.nombre}</h2>
          <button className="close-button" onClick={onClose}>
            <span className="material-icons">close</span>
          </button>
        </div>
        
        <div className="modal-body">
          <div className="recipe-image-section">
            <img 
              src={helado.imagen} 
              alt={helado.nombre}
              className="recipe-image"
            />
            <div className="recipe-info">
              <div className="info-item">
                <span className="material-icons">schedule</span>
                <div>
                  <strong>Tiempo de preparación</strong>
                  <p>{helado.tiempo}</p>
                </div>
              </div>
              <div className="info-item">
                <span className="material-icons">restaurant</span>
                <div>
                  <strong>Porciones</strong>
                  <p>{helado.porciones}</p>
                </div>
              </div>
              <div className="info-item">
                <span className="material-icons">star</span>
                <div>
                  <strong>Dificultad</strong>
                  <p>{helado.dificultad}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="recipe-details">
            <div className="ingredients-section">
              <h3>
                <span className="material-icons">shopping_cart</span>
                Ingredientes
              </h3>
              <ul className="ingredients-list">
                {helado.ingredientes.map((ingrediente, index) => (
                  <li key={index} className="ingredient-item">
                    <span className="ingredient-amount">{ingrediente.cantidad}</span>
                    <span className="ingredient-name">{ingrediente.nombre}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="instructions-section">
              <h3>
                <span className="material-icons">list_alt</span>
                Instrucciones
              </h3>
              <ol className="instructions-list">
                {helado.instrucciones.map((instruccion, index) => (
                  <li key={index} className="instruction-item">
                    <span className="step-number">{index + 1}</span>
                    <p>{instruccion}</p>
                  </li>
                ))}
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RecetaModal
