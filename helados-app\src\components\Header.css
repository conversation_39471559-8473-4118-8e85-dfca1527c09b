/* Estilos del header */
.header {
  background: var(--bg-primary);
  box-shadow: var(--shadow-light);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  transition: transform 0.2s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: 2rem;
  color: var(--primary-red);
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.logo-text {
  background: linear-gradient(45deg, var(--primary-blue), var(--primary-red));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Contenedor de b<PERSON> */
.search-container {
  flex: 1;
  max-width: 500px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-secondary);
  border: 2px solid transparent;
  border-radius: var(--border-radius-large);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
  background: var(--bg-primary);
}

.search-icon {
  color: var(--text-tertiary);
  margin-right: var(--spacing-sm);
  font-size: 1.25rem;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1rem;
  color: var(--text-primary);
  outline: none;
  font-family: inherit;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.clear-search {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.clear-search .material-icons {
  font-size: 1.25rem;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-sm);
  }
  
  .search-container {
    width: 100%;
    max-width: none;
  }
  
  .logo {
    font-size: 1.1rem;
  }
  
  .logo-icon {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: var(--spacing-sm);
  }
  
  .search-box {
    padding: var(--spacing-sm);
  }
  
  .search-input {
    font-size: 0.9rem;
  }
}
