/* Estilos del modal de receta */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  animation: slideUp 0.3s ease;
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Header del modal */
.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 40px;
  height: 40px;
}

.close-button:hover {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.close-button .material-icons {
  font-size: 1.5rem;
}

/* Cuerpo del modal */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

/* Sección de imagen de la receta */
.recipe-image-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.recipe-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.recipe-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.info-item .material-icons {
  color: var(--primary-blue);
  font-size: 1.5rem;
  margin-top: 2px;
}

.info-item strong {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.info-item p {
  color: var(--text-secondary);
  margin: 0;
}

/* Detalles de la receta */
.recipe-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

.ingredients-section,
.instructions-section {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
}

.ingredients-section h3,
.instructions-section h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

.ingredients-section h3 .material-icons,
.instructions-section h3 .material-icons {
  color: var(--primary-green);
  font-size: 1.5rem;
}

/* Lista de ingredientes */
.ingredients-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.ingredient-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-green);
}

.ingredient-amount {
  font-weight: 600;
  color: var(--primary-blue);
  min-width: 80px;
}

.ingredient-name {
  color: var(--text-primary);
}

/* Lista de instrucciones */
.instructions-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.instruction-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
}

.step-number {
  background: var(--primary-blue);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.instruction-item p {
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .modal-backdrop {
    padding: var(--spacing-sm);
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-header {
    padding: var(--spacing-md);
  }
  
  .modal-body {
    padding: var(--spacing-md);
  }
  
  .recipe-image-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .recipe-image {
    height: 200px;
  }
  
  .recipe-details {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .ingredients-section,
  .instructions-section {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .modal-title {
    font-size: 1.25rem;
  }
  
  .recipe-image {
    height: 180px;
  }
  
  .info-item {
    padding: var(--spacing-sm);
  }
  
  .ingredients-section,
  .instructions-section {
    padding: var(--spacing-sm);
  }
  
  .instruction-item {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }
  
  .step-number {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}
