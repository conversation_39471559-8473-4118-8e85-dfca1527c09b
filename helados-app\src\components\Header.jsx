import React from 'react'
import './Header.css'

function Header({ searchTerm, setSearchTerm }) {
  return (
    <header className="header">
      <div className="header-content">
        <div className="logo">
          <span className="material-icons logo-icon">icecream</span>
          <span className="logo-text"><PERSON><PERSON><PERSON></span>
        </div>
        
        <div className="search-container">
          <div className="search-box">
            <span className="material-icons search-icon">search</span>
            <input
              type="text"
              placeholder="Buscar sabores de helado..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="clear-search"
              >
                <span className="material-icons">clear</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
