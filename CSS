/* estilos.css */

/* Variables CSS para colores y fuentes */
:root {
    --color-primary: #8B4513; /* Marrón Chocolate más oscuro */
    --color-secondary: #FFD700; /* Dorado para acentos */
    --color-background: #FDF5E6; /* Crema suave */
    --color-text: #36454F; /* Gris oscuro */
    --color-highlight: #FFFACD; /* Amarillo claro para ingredientes */
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Montserrat', sans-serif;
}

/* Estilos globales */
body {
    font-family: var(--font-body);
    margin: 0;
    background-color: var(--color-background);
    color: var(--color-text);
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Encabezado de la página */
.header {
    background-color: var(--color-primary);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    z-index: 100;
}

.header h1 {
    margin: 0;
    font-family: var(--font-heading);
    font-size: 28px;
    letter-spacing: 1px;
    flex-grow: 1; /* Permite que el título ocupe el espacio */
    text-align: center; /* Centra el título en el header */
}

/* Botón de volver en receta.html */
.back-button {
    background: none;
    border: none;
    color: white;
    font-size: 1.1em;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.back-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Botón de favorito */
.favorito-btn {
    background: none;
    border: 2px solid white;
    color: white;
    font-size: 26px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 50px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.favorito-btn:hover {
    background-color: white;
    color: var(--color-primary);
    transform: scale(1.05);
}

/* Estilos para el corazón de favoritos cuando está "activo" */
.favorito-btn.activo .far {
    display: none;
}
.favorito-btn.activo .fas {
    display: inline-block;
    color: #e74c3c; /* Rojo para el corazón activo */
}

/* Ocultar el corazón lleno por defecto */
.favorito-btn .fas {
    display: none;
}

/* Estilos de la Galería de Recetas (para index.html) */
.galeria-recetas {
    max-width: 1200px;
    margin: 30px auto;
    padding: 20px;
    flex-grow: 1;
    text-align: center;
}

.galeria-recetas h2 {
    font-family: var(--font-heading);
    color: var(--color-primary);
    font-size: 36px;
    margin-bottom: 40px;
    position: relative;
    padding-bottom: 10px;
}

.galeria-recetas h2::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background-color: var(--color-secondary);
    border-radius: 5px;
}

.grid-helados {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Columnas responsivas */
    gap: 30px; /* Espacio entre las tarjetas */
    justify-content: center;
}

.card-helado {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.card-helado:hover {
    transform: translateY(-8px); /* Efecto de elevación */
    box-shadow: 0 12px 25px rgba(0,0,0,0.2);
}

.card-helado img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 3px solid var(--color-secondary);
}

.card-helado-content {
    padding: 20px;
}

.card-helado h3 {
    font-family: var(--font-heading);
    font-size: 24px;
    color: var(--color-primary);
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
    /* Quitar la línea decorativa para los títulos de tarjeta si no la queremos */
    border-bottom: none;
    padding-bottom: 0;
}

.card-helado .btn-ver-receta {
    background-color: var(--color-primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: background-color 0.3s ease;
    text-decoration: none; /* Para que parezca un botón aunque sea un enlace */
    display: inline-block; /* Para que el padding y margen funcionen bien */
    margin-top: 10px;
}

.card-helado .btn-ver-receta:hover {
    background-color: #A0522D; /* Un tono más claro de marrón */
}

/* Estilos de la Receta Individual (para receta.html - se mantienen) */
.contenedor-receta {
    max-width: 850px;
    margin: 30px auto;
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    overflow: hidden;
    flex-grow: 1;
}

.imagen-receta {
    width: 100%;
    height: 350px;
    object-fit: cover;
    border-bottom: 5px solid var(--color-secondary);
}

.info-principal,
.seccion-ingredientes,
.seccion-instrucciones,
.acciones-receta {
    padding: 30px;
}

/* Títulos de sección */
.contenedor-receta h2, .contenedor-receta h3 {
    font-family: var(--font-heading);
    color: var(--color-primary);
    margin-top: 0;
    font-size: 28px;
    text-align: center;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 10px;
}

.contenedor-receta h2::after, .contenedor-receta h3::after {
    content: '';
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--color-secondary);
    border-radius: 5px;
}

/* Información meta (tiempo, porciones) */
.meta-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    color: var(--color-text);
    font-size: 1.15em;
    margin-bottom: 30px;
    padding-top: 10px;
    border-bottom: 1px dashed #ddd;
    padding-bottom: 20px;
}

.meta-info p {
    display: flex;
    align-items: center;
}

.meta-info i {
    margin-right: 8px;
    color: var(--color-primary);
    font-size: 1.2em;
}

/* Listas de ingredientes e instrucciones */
#listaIngredientes,
#listaInstrucciones {
    list-style: none;
    padding: 0;
}

#listaIngredientes li {
    background-color: var(--color-highlight);
    border-left: 5px solid var(--color-secondary);
    margin-bottom: 12px;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 1.05em;
    box-shadow: 2px 2px 5px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
}

#listaIngredientes li:hover {
    transform: translateX(5px);
}

#listaIngredientes li:last-child {
    margin-bottom: 0;
}

#listaInstrucciones {
    counter-reset: step;
}

#listaInstrucciones li {
    margin-bottom: 20px;
    padding-left: 50px;
    position: relative;
    font-size: 1.05em;
}

#listaInstrucciones li::before {
    content: counter(step);
    counter-increment: step;
    background-color: var(--color-primary);
    color: white;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 0;
    font-size: 1em;
    box-shadow: 2px 2px 5px rgba(0,0,0,0.1);
}

/* Botón principal (Añadir a Lista de Compras) */
.btn-principal {
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
    margin: 30px auto 0;
    background-color: var(--color-secondary);
    color: var(--color-primary);
    border: none;
    padding: 15px 35px;
    font-size: 1.3em;
    border-radius: 50px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
    font-weight: 600;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-principal i {
    margin-right: 12px;
    font-size: 1.1em;
}

.btn-principal:hover {
    background-color: #FFC107;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
}

.btn-principal:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Pie de página */
.footer {
    text-align: center;
    padding: 25px;
    background-color: var(--color-primary);
    color: white;
    font-size: 0.9em;
    margin-top: auto;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
}

/* Media Queries para responsividad */
@media (max-width: 768px) {
    .header {
        padding: 15px 20px;
    }
    .header h1 {
        font-size: 24px;
    }
    .favorito-btn {
        font-size: 22px;
        padding: 6px 10px;
    }
    .galeria-recetas h2 {
        font-size: 30px;
    }
    .grid-helados {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }
    .contenedor-receta {
        margin: 20px 10px;
        border-radius: 10px;
    }
    .imagen-receta {
        height: 250px;
    }
    .info-principal, .seccion-ingredientes, .seccion-instrucciones, .acciones-receta {
        padding: 20px;
    }
    .contenedor-receta h2, .contenedor-receta h3 {
        font-size: 24px;
        margin-bottom: 20px;
    }
    .meta-info {
        flex-direction: column;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;
    }
    #listaIngredientes li, #listaInstrucciones li {
        font-size: 1em;
    }
    .btn-principal {
        font-size: 1.1em;
        padding: 12px 25px;
    }
    .back-button {
        font-size: 1em;
        padding: 6px 10px;
    }
}