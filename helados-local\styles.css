/* Reset y variables globales */
:root {
  /* Colores principales inspirados en Google */
  --primary-blue: #4285f4;
  --primary-red: #ea4335;
  --primary-yellow: #fbbc04;
  --primary-green: #34a853;

  /* Colores de fondo */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e8f0fe;

  /* Colores de texto */
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-tertiary: #80868b;

  /* Sombras */
  --shadow-light: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --shadow-medium: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --shadow-heavy: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);

  /* Bordes */
  --border-radius: 8px;
  --border-radius-large: 16px;

  /* Espaciado */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* Tipografía */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reset básico */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--text-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Estilos principales de la aplicación */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.main-content {
  padding: var(--spacing-lg) 0;
}

/* Estilos del header */
.header {
  background: var(--bg-primary);
  box-shadow: var(--shadow-light);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1.25rem;
  transition: transform 0.2s ease;
}

.logo:hover {
  transform: scale(1.05);
}

.logo-icon {
  font-size: 2rem;
  color: var(--primary-red);
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.logo-text {
  background: linear-gradient(45deg, var(--primary-blue), var(--primary-red));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Contenedor de búsqueda */
.search-container {
  flex: 1;
  max-width: 500px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-secondary);
  border: 2px solid transparent;
  border-radius: var(--border-radius-large);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
  background: var(--bg-primary);
}

.search-icon {
  color: var(--text-tertiary);
  margin-right: var(--spacing-sm);
  font-size: 1.25rem;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1rem;
  color: var(--text-primary);
  outline: none;
  font-family: inherit;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.clear-search {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.clear-search .material-icons {
  font-size: 1.25rem;
}

/* Sección hero */
.hero-section {
  text-align: center;
  padding: var(--spacing-xxl) var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero-icon {
  font-size: 1.2em;
  color: var(--primary-red);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.hero-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Grid de helados */
.helados-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  padding: 0 var(--spacing-md);
  max-width: 1200px;
  margin: 0 auto;
}

/* Estado sin resultados */
.no-results {
  text-align: center;
  padding: var(--spacing-xxl);
  color: var(--text-secondary);
}

.no-results .material-icons {
  font-size: 4rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.no-results p {
  font-size: 1rem;
}

/* Estilos de las tarjetas de helado */
.helado-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.helado-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
}

/* Contenedor de imagen */
.card-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.helado-card:hover .card-image {
  transform: scale(1.1);
}

/* Overlay de la imagen */
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(66, 133, 244, 0.9) 0%,
    rgba(234, 67, 53, 0.9) 100%
  );
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-weight: 600;
  gap: var(--spacing-xs);
}

.helado-card:hover .card-overlay {
  opacity: 1;
}

.card-overlay .material-icons {
  font-size: 2rem;
}

/* Contenido de la tarjeta */
.card-content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

.card-sabor {
  color: var(--primary-blue);
  font-weight: 500;
  font-size: 0.9rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Meta información */
.card-meta {
  display: flex;
  gap: var(--spacing-md);
  margin: var(--spacing-sm) 0;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.meta-item .material-icons {
  font-size: 1rem;
  color: var(--text-tertiary);
}

/* Tags */
.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: auto;
}

.tag {
  background: var(--bg-tertiary);
  color: var(--primary-blue);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Estados especiales */
.helado-card:active {
  transform: translateY(-4px);
}

/* Estilos del modal de receta */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  animation: slideUp 0.3s ease;
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Header del modal */
.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.close-button {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 40px;
  height: 40px;
}

.close-button:hover {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.close-button .material-icons {
  font-size: 1.5rem;
}

/* Cuerpo del modal */
.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

/* Sección de imagen de la receta */
.recipe-image-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.recipe-image {
  width: 100%;
  height: 250px;
  object-fit: cover;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.recipe-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.info-item .material-icons {
  color: var(--primary-blue);
  font-size: 1.5rem;
  margin-top: 2px;
}

.info-item strong {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.info-item p {
  color: var(--text-secondary);
  margin: 0;
}

/* Detalles de la receta */
.recipe-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

.ingredients-section,
.instructions-section {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
}

.ingredients-section h3,
.instructions-section h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

.ingredients-section h3 .material-icons,
.instructions-section h3 .material-icons {
  color: var(--primary-green);
  font-size: 1.5rem;
}

/* Lista de ingredientes */
.ingredients-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.ingredient-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-green);
}

.ingredient-amount {
  font-weight: 600;
  color: var(--primary-blue);
  min-width: 80px;
}

.ingredient-name {
  color: var(--text-primary);
}

/* Lista de instrucciones */
.instructions-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.instruction-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
}

.step-number {
  background: var(--primary-blue);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.instruction-item p {
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .search-container {
    width: 100%;
    max-width: none;
  }

  .logo {
    font-size: 1.1rem;
  }

  .logo-icon {
    font-size: 1.75rem;
  }

  .hero-title {
    font-size: 2.5rem;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .hero-subtitle {
    font-size: 1.1rem;
    padding: 0 var(--spacing-md);
  }

  .helados-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .main-content {
    padding: var(--spacing-md) 0;
  }

  .card-image-container {
    height: 180px;
  }

  .card-content {
    padding: var(--spacing-md);
  }

  .card-title {
    font-size: 1.1rem;
  }

  .card-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .modal-backdrop {
    padding: var(--spacing-sm);
  }

  .modal-content {
    max-height: 95vh;
  }

  .modal-header {
    padding: var(--spacing-md);
  }

  .modal-body {
    padding: var(--spacing-md);
  }

  .recipe-image-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .recipe-image {
    height: 200px;
  }

  .recipe-details {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .ingredients-section,
  .instructions-section {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: var(--spacing-lg) var(--spacing-sm);
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .header-content {
    padding: var(--spacing-sm);
  }

  .search-box {
    padding: var(--spacing-sm);
  }

  .search-input {
    font-size: 0.9rem;
  }

  .card-image-container {
    height: 160px;
  }

  .card-content {
    padding: var(--spacing-sm);
  }

  .card-title {
    font-size: 1rem;
  }

  .card-sabor {
    font-size: 0.8rem;
  }

  .meta-item {
    font-size: 0.8rem;
  }

  .tag {
    font-size: 0.7rem;
    padding: 2px var(--spacing-xs);
  }

  .modal-title {
    font-size: 1.25rem;
  }

  .recipe-image {
    height: 180px;
  }

  .info-item {
    padding: var(--spacing-sm);
  }

  .ingredients-section,
  .instructions-section {
    padding: var(--spacing-sm);
  }

  .instruction-item {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .step-number {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }
}
